import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { Zhu<PERSON><PERSON>omian } from '../../zujian/minganbuju/yangshihuazujian.js';

// 轮播容器
const Lu<PERSON><PERSON><PERSON><PERSON> = styled(<PERSON><PERSON><PERSON><PERSON><PERSON>)`
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  overflow: hidden;
  cursor: pointer;
  
  @media (max-width: 768px) {
    height: 150px;
  }
`;

// 广告图片
const Guanggaotupian = styled(motion.img)`
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
`;

// 指示器容器
const Zhishiqirongqi = styled.div`
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
`;

// 指示器点
const <PERSON><PERSON><PERSON>qi<PERSON> = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.huoyue 
    ? props.theme.yanse.zhuyao 
    : 'rgba(255, 255, 255, 0.5)'};
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  cursor: pointer;
  
  &:hover {
    background: ${props => props.theme.yanse.zhuyao};
    transform: scale(1.2);
  }
`;



// 轮播广告组件
function Lunboguanggao() {
  const [dangqiansuoyin, shedangqiansuoyin] = useState(0);
  const [zidongbofang, shezidongbofang] = useState(true);
  
  // 网关地址
  const wangguandizhi = 'http://127.0.0.1:8098';
  
  // 广告图片数组
  const guanggaotupian = Array.from({ length: 6 }, (_, index) => ({
    id: index + 1,
    url: `${wangguandizhi}/jiekou/ziyuanhuoqu/guanggao/guanggao${index + 1}.png`,
    alt: `广告 ${index + 1}`
  }));
  
  // 自动轮播
  useEffect(() => {
    if (!zidongbofang) return;
    
    const jiangeqi = setInterval(() => {
      shedangqiansuoyin(prev => (prev + 1) % guanggaotupian.length);
    }, 4000); // 4秒切换一次
    
    return () => clearInterval(jiangeqi);
  }, [zidongbofang, guanggaotupian.length]);
  
  // 切换到指定索引
  const qiehuandao = (suoyin) => {
    shedangqiansuoyin(suoyin);
  };
  
  // 鼠标悬停暂停自动播放
  const chulishubiaoruyu = () => {
    shezidongbofang(false);
  };
  
  // 鼠标离开恢复自动播放
  const chulishubiaolikai = () => {
    shezidongbofang(true);
  };
  
  // 动画配置
  const donghuapeizhi = {
    initial: { opacity: 0, scale: 1.1 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
    transition: { duration: 0.5, ease: "easeInOut" }
  };
  
  return (
    <Lunborongqi
      onMouseEnter={chulishubiaoruyu}
      onMouseLeave={chulishubiaolikai}
    >
      {/* 广告图片 */}
      <AnimatePresence mode="wait">
        <Guanggaotupian
          key={dangqiansuoyin}
          src={guanggaotupian[dangqiansuoyin].url}
          alt={guanggaotupian[dangqiansuoyin].alt}
          variants={donghuapeizhi}
          initial="initial"
          animate="animate"
          exit="exit"
          onError={(e) => {
            // 图片加载失败时的处理
            console.warn(`广告图片加载失败: ${e.target.src}`);
          }}
        />
      </AnimatePresence>
      

      
      {/* 指示器 */}
      <Zhishiqirongqi>
        {guanggaotupian.map((_, suoyin) => (
          <Zhishiqidian
            key={suoyin}
            huoyue={suoyin === dangqiansuoyin}
            onClick={() => qiehuandao(suoyin)}
          />
        ))}
      </Zhishiqirongqi>
    </Lunborongqi>
  );
}

export default Lunboguanggao;
