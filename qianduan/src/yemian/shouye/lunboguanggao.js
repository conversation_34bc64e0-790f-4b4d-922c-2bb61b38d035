import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON>omi<PERSON> } from '../../zujian/minganbuju/yangshihua<PERSON>jian.js';

// 轮播容器
const Lu<PERSON><PERSON><PERSON><PERSON> = styled.div`
  position: relative;
  width: 100%;
  height: auto;
  cursor: pointer;
  user-select: none;

  &:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
  }

  @media (max-width: 768px) {
    margin: 10px;
    width: calc(100% - 20px);

    &:hover {
      transform: none;
    }
  }
`;

// 广告图片
const Guanggaotupian = styled(motion.img)`
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  border-radius: 0 0 20px 0;

  @media (max-width: 768px) {
    border-radius: 15px;
  }
`;

// 指示器容器
const <PERSON>hishiqirongqi = styled.div`
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 20;
  background: rgba(0, 0, 0, 0.4);
  padding: 10px 16px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
`;

// 指示器点
const Zhishiqidian = styled.div`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.huoyue
    ? '#ffffff'
    : 'rgba(255, 255, 255, 0.6)'};
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid ${props => props.huoyue ? '#ffffff' : 'transparent'};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  &:hover {
    background: #ffffff;
    transform: scale(1.2);
    border: 2px solid #ffffff;
  }
`;



// 轮播广告组件
function Lunboguanggao() {
  const [dangqiansuoyin, shedangqiansuoyin] = useState(0);
  const [zidongbofang, shezidongbofang] = useState(true);
  const [shoujiduanhuadong, sheshoujiduanhuadong] = useState(false);
  const lunborongqiref = useRef(null);
  
  // 网关地址
  const wangguandizhi = 'http://127.0.0.1:8098';
  
  // 广告图片数组
  const guanggaotupian = Array.from({ length: 6 }, (_, index) => ({
    id: index + 1,
    url: `${wangguandizhi}/jiekou/ziyuanhuoqu/guanggao/guanggao${index + 1}.png`,
    alt: `广告 ${index + 1}`
  }));
  
  // 自动轮播
  useEffect(() => {
    if (!zidongbofang) return;

    const jiangeqi = setInterval(() => {
      shedangqiansuoyin(prev => (prev + 1) % guanggaotupian.length);
    }, 3000); // 3秒切换一次

    return () => clearInterval(jiangeqi);
  }, [zidongbofang, guanggaotupian.length]);
  
  // 切换到指定索引
  const qiehuandao = (suoyin) => {
    shedangqiansuoyin(suoyin);
    // 手动切换后暂停自动播放3秒
    shezidongbofang(false);
    setTimeout(() => {
      shezidongbofang(true);
    }, 3000);
  };
  
  // 鼠标悬停暂停自动播放
  const chulishubiaoruyu = () => {
    shezidongbofang(false);
  };
  
  // 鼠标离开恢复自动播放
  const chulishubiaolikai = () => {
    shezidongbofang(true);
  };

  // 处理点击跳转
  const chulidianjitiaozhuang = () => {
    if (!shoujiduanhuadong) {
      window.open('https://luoluo.blyfw.cn', '_blank');
    }
  };

  // 处理手势滑动结束
  const chulihuadongjieshu = (event, info) => {
    const { offset, velocity } = info;
    const huadongminjueli = 50; // 最小滑动距离
    const suduyu = 500; // 速度阈值

    if (Math.abs(offset.x) > huadongminjueli || Math.abs(velocity.x) > suduyu) {
      if (offset.x > 0) {
        // 向右滑动，显示上一张
        shedangqiansuoyin(prev =>
          prev === 0 ? guanggaotupian.length - 1 : prev - 1
        );
      } else {
        // 向左滑动，显示下一张
        shedangqiansuoyin(prev => (prev + 1) % guanggaotupian.length);
      }

      // 滑动后暂停自动播放3秒
      shezidongbofang(false);
      setTimeout(() => {
        shezidongbofang(true);
      }, 3000);
    }

    // 延迟重置滑动状态
    setTimeout(() => {
      sheshoujiduanhuadong(false);
    }, 100);
  };

  // 处理滑动开始
  const chulihuadongkaishi = () => {
    sheshoujiduanhuadong(true);
  };

  // 处理指示器点击（阻止事件冒泡）
  const chulizhishiqidianjishijian = (event, suoyin) => {
    event.stopPropagation(); // 阻止事件冒泡到父容器
    qiehuandao(suoyin);
  };
  
  // 动画配置
  const donghuapeizhi = {
    initial: { opacity: 0, scale: 1.1 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
    transition: { duration: 0.5, ease: "easeInOut" }
  };
  
  return (
    <Lunborongqi
      ref={lunborongqiref}
      onMouseEnter={chulishubiaoruyu}
      onMouseLeave={chulishubiaolikai}
      onClick={chulidianjitiaozhuang}
    >
      {/* 广告图片 */}
      <AnimatePresence mode="wait">
        <Guanggaotupian
          key={dangqiansuoyin}
          src={guanggaotupian[dangqiansuoyin].url}
          alt={guanggaotupian[dangqiansuoyin].alt}
          variants={donghuapeizhi}
          initial="initial"
          animate="animate"
          exit="exit"
          drag="x"
          dragConstraints={{ left: 0, right: 0 }}
          dragElastic={0.2}
          onDragStart={chulihuadongkaishi}
          onDragEnd={chulihuadongjieshu}
          onError={(e) => {
            // 图片加载失败时的处理
            console.warn(`广告图片加载失败: ${e.target.src}`);
          }}
          onLoad={(e) => {
            // 图片加载完成后，确保容器高度适应图片
            const img = e.target;
            const container = img.parentElement;
            if (container) {
              container.style.height = `${img.offsetHeight}px`;
            }
          }}
        />
      </AnimatePresence>
      

      
      {/* 指示器 */}
      <Zhishiqirongqi>
        {guanggaotupian.map((_, suoyin) => (
          <Zhishiqidian
            key={suoyin}
            huoyue={suoyin === dangqiansuoyin}
            onClick={(event) => chulizhishiqidianjishijian(event, suoyin)}
          />
        ))}
      </Zhishiqirongqi>
    </Lunborongqi>
  );
}

export default Lunboguanggao;
