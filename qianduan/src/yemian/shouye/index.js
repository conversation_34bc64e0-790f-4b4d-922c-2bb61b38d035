import React from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON>rognqi } from '../../zujian/minganbuju/yangshihuazujian.js';
import Lunboguanggao from './lunboguanggao.js';

// 首页主容器
const <PERSON><PERSON><PERSON><PERSON><PERSON> = styled(<PERSON><PERSON>rognqi)`
  min-height: 100vh;
  padding: 80px 20px 40px;
  display: flex;
  position: relative;

  @media (max-width: 768px) {
    flex-direction: column;
    padding: 80px 10px 20px;
  }
`;

// 左侧广告区域
const Z<PERSON><PERSON>guanggaoquyu = styled.div`
  width: 500px;
  position: sticky;
  top: 100px;
  height: fit-content;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  margin-right: 20px;

  @media (max-width: 768px) {
    width: 100%;
    position: static;
    max-height: none;
    display: flex;
    justify-content: center;
    margin-right: 0;
    margin-bottom: 20px;
  }
`;

// 主内容区域
const Zhuneirongquyu = styled.div`
  flex: 1;

  @media (max-width: 768px) {
    /* 手机端无需额外样式 */
  }
`;

// 首页组件
function Shouye() {
  return (
    <Shouyerongqi>
      {/* 左侧广告区域 */}
      <Zuoceguanggaoquyu>
        <Lunboguanggao />
      </Zuoceguanggaoquyu>

      {/* 主内容区域 */}
      <Zhuneirongquyu>
        {/* 这里可以放置主要内容 */}
      </Zhuneirongquyu>
    </Shouyerongqi>
  );
}

export default Shouye;
