import React from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON>nqi } from '../../zujian/minganbuju/yangshihua<PERSON>jian.js';
import Lunboguanggao from './lunboguanggao.js';

// 首页主容器
const <PERSON><PERSON><PERSON><PERSON><PERSON> = styled(<PERSON><PERSON><PERSON>nqi)`
  min-height: 100vh;
  padding: 80px 0 40px 0;
  display: flex;
  position: relative;

  @media (max-width: 768px) {
    flex-direction: column;
    padding: 80px 0 20px 0;
  }
`;

// 左侧广告区域
const Zuoceguanggaoquyu = styled.div`
  width: 500px;
  position: sticky;
  top: 80px;
  height: fit-content;
  max-height: calc(100vh - 80px);
  overflow-y: auto;
  margin: 0;

  @media (max-width: 768px) {
    width: 100%;
    position: static;
    max-height: none;
    margin: 0;
  }
`;

// 主内容区域
const Zhuneirongquyu = styled.div`
  flex: 1;
  padding: 0 20px;

  @media (max-width: 768px) {
    padding: 0 10px;
  }
`;

// 首页组件
function Shouye() {
  return (
    <Shou<PERSON>ongqi>
      {/* 左侧广告区域 */}
      <Zuoceguanggaoquyu>
        <Lunboguanggao />
      </Zuoceguanggaoquyu>

      {/* 主内容区域 */}
      <Zhuneirongquyu>
        {/* 这里可以放置主要内容 */}
      </Zhuneirongquyu>
    </Shouyerongqi>
  );
}

export default Shouye;
