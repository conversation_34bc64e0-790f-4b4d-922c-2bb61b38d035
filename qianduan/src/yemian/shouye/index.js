import React from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON>n<PERSON> } from '../../zujian/minganbuju/yangshihua<PERSON>jian.js';
import Lunboguanggao from './lunboguanggao.js';

// 首页主容器
const S<PERSON><PERSON><PERSON><PERSON> = styled(<PERSON><PERSON>nqi)`
  min-height: 100vh;
  padding: 80px 20px 40px;
  display: flex;
  position: relative;

  @media (max-width: 768px) {
    flex-direction: column;
    padding: 80px 10px 20px;
  }
`;

// 主内容区域
const Zhuneirongquyu = styled.div`
  flex: 1;
  padding-right: 20px;

  @media (max-width: 768px) {
    padding-right: 0;
    margin-bottom: 20px;
  }
`;

// 右侧广告区域
const Youceguanggaoquyu = styled.div`
  width: 300px;
  position: sticky;
  top: 100px;
  height: fit-content;
  max-height: calc(100vh - 120px);
  overflow-y: auto;

  @media (max-width: 768px) {
    width: 100%;
    position: static;
    max-height: none;
    display: flex;
    justify-content: center;
  }
`;

// 首页组件
function Shouye() {
  return (
    <Shouyerongqi>
      {/* 主内容区域 */}
      <Zhuneirongquyu>
        {/* 这里可以放置主要内容 */}
      </Zhuneirongquyu>

      {/* 右侧广告区域 */}
      <Youceguanggaoquyu>
        <Lunboguanggao />
      </Youceguanggaoquyu>
    </Shouyerongqi>
  );
}

export default Shouye;
