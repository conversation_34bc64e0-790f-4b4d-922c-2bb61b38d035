import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useShi<PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../zujian/minganbuju/zhutitiqigong.js';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Wanggebuju,
  Donghuarongqi
} from '../../zujian/minganbuju/yangshihuazujian.js';

// 首页主容器
const S<PERSON> = styled(<PERSON><PERSON>qi)`
  min-height: 100vh;
  padding: 80px 20px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  overflow-x: hidden;
`;

// 欢迎区域
const Hu<PERSON><PERSON><PERSON><PERSON><PERSON> = styled(<PERSON><PERSON><PERSON><PERSON><PERSON>)`
  text-align: center;
  padding: 60px 40px;
  border-radius: ${props => props.theme.yuanjiao.da};
  margin-bottom: 40px;
  max-width: 800px;
  width: 100%;
  backdrop-filter: blur(10px);
  border: 1px solid ${props => props.theme.yanse.biankuang};
`;

// 主标题
const <PERSON><PERSON><PERSON> = styled(<PERSON><PERSON><PERSON><PERSON>)`
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(135deg,
    ${props => props.theme.yanse.zhuyao} 0%,
    ${props => props.theme.yanse.zhuyao_qian} 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

// 副标题
const Fubianti = styled(Zhutiwenben)`
  font-size: 1.2rem;
  opacity: 0.8;
  margin-bottom: 30px;
  line-height: 1.6;
`;

// 功能卡片网格
const Gongnengwangge = styled(Wanggebuju)`
  max-width: 1200px;
  width: 100%;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 40px;
`;

// 功能卡片
const Gongnenkapian = styled(motion.div)`
  background: ${props => props.theme.yanse.beijing_er};
  border: 1px solid ${props => props.theme.yanse.biankuang};
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  backdrop-filter: blur(10px);
  
  &:hover {
    transform: translateY(-5px);
    border-color: ${props => props.theme.yanse.zhuyao};
    box-shadow: 0 10px 30px ${props => props.theme.yanse.yinying};
  }
`;

// 卡片图标
const Kapiantubiao = styled.div`
  font-size: 3rem;
  margin-bottom: 20px;
  color: ${props => props.theme.yanse.zhuyao};
`;

// 卡片标题
const Kapianbianti = styled(Zhutiwenben)`
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
`;

// 卡片描述
const Kapianmiaoshu = styled(Zhutiwenben)`
  opacity: 0.7;
  line-height: 1.5;
  font-size: 0.95rem;
`;

// 动画配置
const donghuapeizhi = {
  initial: { opacity: 0, y: 50 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
};

const kapiandonghau = {
  initial: { opacity: 0, scale: 0.9 },
  animate: { opacity: 1, scale: 1 },
  whileHover: { scale: 1.02 },
  transition: { duration: 0.4, ease: "easeOut" }
};

// 功能数据
const gongnengshuji = [
  {
    tubiao: '👹',
    bianti: '怪物数据',
    miaoshu: '查询游戏中所有怪物的详细信息，包括属性、掉落物品等',
    lianjie: '/guaiwushuju'
  },
  {
    tubiao: '🎒',
    bianti: '物品数据', 
    miaoshu: '浏览游戏中的装备、道具、材料等物品信息',
    lianjie: '/wupinshuju'
  },
  {
    tubiao: '🗺️',
    bianti: '地图数据',
    miaoshu: '探索游戏世界的地图信息和区域详情',
    lianjie: '/ditushuju'
  },
  {
    tubiao: '⚔️',
    bianti: '技能数据',
    miaoshu: '了解各职业的技能效果和学习条件',
    lianjie: '/jinengshuju'
  }
];

// 首页组件
function shouye() {
  const chulidianjishijian = (lianjie) => {
    window.location.href = lianjie;
  };

  return (
    <Donghuarongqi
      initial="initial"
      animate="animate"
      variants={donghuapeizhi}
    >
      <Shouyerongqi>
        {/* 欢迎区域 */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <Huanyingquyu>
            <Zhubianti>RO百科</Zhubianti>
            <Fubianti>
              专业的仙境传说资料站<br />
              提供全面的游戏数据查询服务
            </Fubianti>
          </Huanyingquyu>
        </motion.div>

        {/* 功能卡片网格 */}
        <Gongnengwangge>
          {gongnengshuji.map((xiangmu, suoyin) => (
            <Gongnenkapian
              key={suoyin}
              variants={kapiandonghau}
              initial="initial"
              animate="animate"
              whileHover="whileHover"
              transition={{ delay: 0.1 * suoyin }}
              onClick={() => chulidianjishijian(xiangmu.lianjie)}
            >
              <Kapiantubiao>{xiangmu.tubiao}</Kapiantubiao>
              <Kapianbianti>{xiangmu.bianti}</Kapianbianti>
              <Kapianmiaoshu>{xiangmu.miaoshu}</Kapianmiaoshu>
            </Gongnenkapian>
          ))}
        </Gongnengwangge>
      </Shouyerongqi>
    </Donghuarongqi>
  );
}

export default shouye;
